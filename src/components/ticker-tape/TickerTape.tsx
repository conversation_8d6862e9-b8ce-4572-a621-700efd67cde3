'use client';

import React, { memo } from 'react';
import { cn } from '@/lib/utils';
import { useTickerTape, useTickerTapeTheme } from './useTickerTape';
import { validateSymbols, DEFAULT_CONFIG } from './TickerTape.config';
import type { TickerTapeProps, TickerTapeConfig } from './TickerTape.types';

/**
 * TradingView Ticker Tape Component
 *
 * A reusable component that displays a TradingView ticker tape widget with
 * automatic theme detection and configurable symbol sets.
 */
function TickerTapeComponent({
  symbols,
  className,
  displayMode = DEFAULT_CONFIG.displayMode,
  showSymbolLogo = DEFAULT_CONFIG.showSymbolLogo,
  isTransparent = DEFAULT_CONFIG.isTransparent,
  largeChartUrl = DEFAULT_CONFIG.largeChartUrl,
  locale = DEFAULT_CONFIG.locale,
}: TickerTapeProps) {
  const colorTheme = useTickerTapeTheme();

  // Smart transparency: Force opaque in dark mode to ensure proper theming
  const smartTransparent = colorTheme === 'dark' ? false : isTransparent;

  // Prepare configuration for the hook (must call hooks before any early returns
  const config: TickerTapeConfig = {
    symbols,
    colorTheme,
    locale,
    largeChartUrl,
    isTransparent: smartTransparent,
    showSymbolLogo,
    displayMode,
  };

  const { containerRef, isLoading, error } = useTickerTape(config);

  // Validate symbols after hooks
  if (!validateSymbols(symbols)) {
    console.error('TickerTape: Invalid symbols provided', symbols);
    return (
      <div
        className={cn(
          'ticker-tape-error p-4 text-center text-red-500',
          className
        )}
      >
        <p>Error: Invalid ticker symbols provided</p>
        <p className="text-xs text-muted-foreground mt-1">
          Please check that all symbols are in the correct format (e.g.,
          &quot;FWB:SAP&quot;)
        </p>
      </div>
    );
  }

  // Handle errors
  if (error) {
    return (
      <div className={cn('ticker-tape-error p-4 text-center', className)}>
        <div className="rounded-md bg-destructive/10 p-3">
          <p className="text-sm text-destructive">
            Failed to load ticker tape: {error}
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            Please check your internet connection and try again.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      key={`ticker-${colorTheme}`}
      className={cn('ticker-tape-wrapper relative w-full', className)}
    >
      {/* Static HTML structure like the original working example */}
      <div
        className="tradingview-widget-container"
        ref={containerRef}
        role="region"
        aria-label="Stock ticker tape"
        aria-live="polite"
      >
        <div className="tradingview-widget-container__widget"></div>
      </div>

      {/* Loading skeleton - absolutely positioned overlay */}
      {isLoading && (
        <div
          className={cn(
            'absolute inset-0 h-16 animate-pulse flex items-center justify-center',
            isTransparent ? 'bg-transparent' : 'bg-muted'
          )}
        >
          <div className="flex gap-2">
            <div className="size-2 bg-muted-foreground/50 rounded-full animate-bounce" />
            <div className="size-2 bg-muted-foreground/50 rounded-full animate-bounce [animation-delay:0.1s]" />
            <div className="size-2 bg-muted-foreground/50 rounded-full animate-bounce [animation-delay:0.2s]" />
          </div>
        </div>
      )}
    </div>
  );
}

export const TickerTape = memo(TickerTapeComponent);
export default TickerTape;
