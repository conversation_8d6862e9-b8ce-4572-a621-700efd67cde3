import config from '@payload-config';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { getPayload } from 'payload';
import { htmlToLexical } from '@/lib/utils/html-to-lexical';
import { lexicalToText, lexicalToHTML } from '@/lib/utils/lexical';
import { translateToGerman } from '@/lib/integrations/openai/german-translation';

export async function POST(request: NextRequest) {
  try {
    const { articleId } = await request.json();

    if (!articleId) {
      console.error('❌ No articleId provided in request');
      return NextResponse.json(
        { success: false, error: 'Article ID is required' },
        { status: 400 }
      );
    }

    console.log(`🚀 Starting German translation for article: ${articleId}`);
    console.log(
      '📋 Working with saved database content only (save-first workflow)'
    );

    const payload = await getPayload({ config });

    // 🔧 DATABASE TIMING FIX: Ensure fresh database read after save
    // Small delay to allow form save to fully commit to database before reading
    // This prevents race condition where translate reads stale data immediately after save
    await new Promise(resolve => setTimeout(resolve, 500));

    // Fetch the article to get existing data and validate eligibility
    const article = await payload.findByID({
      collection: 'articles',
      id: articleId,
      // Override access control for internal API operations
      overrideAccess: true,
    });

    if (!article) {
      console.error(`❌ Article not found with ID: ${articleId}`);
      return NextResponse.json(
        { success: false, error: 'Article not found' },
        { status: 404 }
      );
    }

    console.log(`📄 Article found: ${article.title}`);
    console.log(`📊 Article workflow stage: ${article.workflowStage}`);
    console.log(`📊 Article type: ${article.articleType}`);

    // 🔍 DEBUG: Log complete article structure for curated articles
    if (article.articleType === 'curated') {
      console.log('🔍 CURATED ARTICLE DEBUG - Complete englishTab structure:');
      console.log(JSON.stringify(article.englishTab, null, 2));
      console.log('🔍 CURATED ARTICLE DEBUG - enhancedContent type and value:');
      console.log('  Type:', typeof article.englishTab?.enhancedContent);
      console.log('  Value:', article.englishTab?.enhancedContent);
      console.log('🔍 CURATED ARTICLE DEBUG - enhancedTitle:');
      console.log('  enhancedTitle:', article.englishTab?.enhancedTitle);
      console.log('  main title:', article.title);
    }

    // Validate article is eligible for translation (both generated and curated articles)
    if (!['generated', 'curated'].includes(article.articleType)) {
      console.error(`❌ Article type not eligible: ${article.articleType}`);
      return NextResponse.json(
        {
          success: false,
          error: 'Article type must be generated or curated for translation',
        },
        { status: 400 }
      );
    }

    // SAVE-FIRST WORKFLOW: Work with database content only
    // No form data handling - article must be saved with content before translation

    // Validate article has required English content in database
    // SAVE-FIRST WORKFLOW: Content must be saved to englishTab before translation
    const hasRequiredContent =
      (article.englishTab?.enhancedTitle || article.title) &&
      article.englishTab?.enhancedContent;

    if (!hasRequiredContent) {
      console.error(
        '❌ Article missing required English content for translation'
      );
      console.log(
        '  Title available:',
        !!(article.englishTab?.enhancedTitle || article.title)
      );
      console.log(
        '  Enhanced content available:',
        !!article.englishTab?.enhancedContent
      );
      console.log(
        '  💡 Tip: Add content to the English Content tab and save before translation'
      );
      return NextResponse.json(
        {
          success: false,
          error:
            'Article must have content in the English Content tab before translation. Please add content and save the article first.',
        },
        { status: 400 }
      );
    }

    // Allow re-translation if German translation already exists
    const isReTranslation = !!article.germanTab?.germanTitle;
    if (isReTranslation) {
      console.log('🔄 Re-translating existing German content');
      console.log('  Previous German title:', article.germanTab?.germanTitle);
      console.log('  Will translate from current English content');
    } else {
      console.log('🆕 Initial German translation');
    }

    // Extract English content for translation from database
    console.log('📄 Using saved database content for translation');

    // Fallback to main title if enhanced title not available (for curated articles)
    const englishTitle = article.englishTab?.enhancedTitle || article.title;
    const englishSummary = article.englishTab?.enhancedSummary || '';

    // ✅ FIX: Add content fallback logic for curated articles
    // Priority: enhancedContent (if not empty) → originalContent → empty
    let englishContentText = '';

    // Try enhanced content first
    if (article.englishTab?.enhancedContent) {
      englishContentText = lexicalToHTML(article.englishTab.enhancedContent);
    }

    // If enhanced content is empty, try original content as fallback
    if (!englishContentText && article.sourcesTab?.originalContent) {
      console.log('🔍 DEBUG: originalContent structure:');
      console.log(JSON.stringify(article.sourcesTab.originalContent, null, 2));
      englishContentText = lexicalToHTML(article.sourcesTab.originalContent);
      console.log(
        '📄 Using original content as fallback (enhanced content was empty)'
      );
      console.log(
        '📄 Original content converted to HTML length:',
        englishContentText.length
      );
    }

    const englishKeyInsights = article.englishTab?.enhancedKeyInsights
      ? article.englishTab.enhancedKeyInsights.map((item: any) => item.insight)
      : [];
    const englishKeywords =
      article.englishTab?.keywords?.map((item: any) => item.keyword) || [];

    // ✅ FIX: Allow title-only translation for curated articles with empty content
    if (!englishTitle) {
      console.error('❌ Missing required English title for translation');
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required English title for translation',
        },
        { status: 400 }
      );
    }

    // If no content available, use title as minimal content for curated articles
    if (!englishContentText && article.articleType === 'curated') {
      englishContentText = `<p>Article about: ${englishTitle}</p>`;
      console.log(
        '📄 Using title-based minimal content for curated article with empty content'
      );
    }

    // Final validation - must have both title and content
    if (!englishContentText) {
      console.error('❌ Missing required English content for translation');
      console.log('  Title present:', !!englishTitle, '- Value:', englishTitle);
      console.log(
        '  Content present:',
        !!englishContentText,
        '- Length:',
        englishContentText.length
      );
      console.log('  Article type:', article.articleType);
      return NextResponse.json(
        {
          success: false,
          error: `Missing required English content for translation. Article type: ${article.articleType}`,
        },
        { status: 400 }
      );
    }

    // Log structure preservation info
    if (englishContentText.length > 0) {
      const contentSample = englishContentText.substring(0, 200);
      const hasHtmlTags = /<[^>]+>/.test(contentSample);
      console.log('🔍 Content structure analysis:');
      console.log(
        '  Format:',
        hasHtmlTags ? 'HTML (structure preserved)' : 'Plain text'
      );
      console.log('  Sample:', contentSample + '...');
      if (hasHtmlTags) {
        const listCount = (englishContentText.match(/<li>/g) || []).length;
        const tableCount = (englishContentText.match(/<table>/g) || []).length;
        const headingCount = (englishContentText.match(/<h[1-6]>/g) || [])
          .length;
        console.log('  Complex elements:', {
          lists: listCount,
          tables: tableCount,
          headings: headingCount,
        });
      }
    }

    // Perform German translation
    const translationResult = await translateToGerman(
      {
        title: englishTitle,
        summary: englishSummary,
        content: englishContentText,
        keyInsights: englishKeyInsights,
        keywords: englishKeywords,
      },
      {
        temperature: 0.3, // Lower temperature for more literal translations
        includeProcessingMetadata: true,
      }
    );

    if (!translationResult.success || !translationResult.data) {
      console.error('❌ German translation failed:', translationResult.error);
      return NextResponse.json(
        {
          success: false,
          error: `German translation failed: ${translationResult.error}`,
        },
        { status: 500 }
      );
    }

    console.log('✅ German translation successful');
    console.log(
      `📊 Performance: ${translationResult.metrics.processingTime}ms`
    );

    // Extract translated German content
    const translatedData = translationResult.data;
    const germanTitle = translatedData.germanTitle;
    const germanSummary = translatedData.germanSummary;
    const germanContent = translatedData.germanContent;
    const germanKeyInsights = translatedData.germanKeyInsights;
    const germanKeywords = translatedData.germanKeywords;

    console.log('📝 German translation parsing:');
    console.log('  Title:', germanTitle);
    console.log('  Content length:', germanContent.length);
    console.log('  Key insights:', germanKeyInsights.length, 'insights');
    console.log('  Keywords:', germanKeywords.length, 'keywords');

    // Convert HTML content to Lexical format
    console.log('🔄 Converting German content to Lexical format...');
    const htmlResult = await htmlToLexical(germanContent);

    if (!htmlResult.metrics.success) {
      console.error('❌ HTML to Lexical conversion failed:', htmlResult);
      return NextResponse.json(
        {
          success: false,
          error: 'HTML to Lexical conversion failed',
          details: htmlResult.metrics,
        },
        { status: 500 }
      );
    }

    const germanContentLexical = htmlResult.result;

    // Strip HTML formatting from title
    const stripHtml = (text: string): string => {
      return text
        .replace(/<[^>]*>/g, '') // Remove all HTML tags
        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
        .replace(/&amp;/g, '&') // Replace HTML entities
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();
    };

    const cleanGermanTitle = stripHtml(germanTitle);
    console.log('🧹 Title cleaning:', germanTitle, '→', cleanGermanTitle);

    // Prepare update data with German translated content
    const updateData: any = {
      hasGermanTranslation: true, // Set flag to show German tab
      germanTab: {
        germanTitle: cleanGermanTitle,
        germanContent: germanContentLexical,
        germanSummary: germanSummary,
        germanKeyInsights: germanKeyInsights.map((insight: string) => ({
          insight,
        })),
        germanKeywords: germanKeywords.map((keyword: string) => ({
          keyword,
        })),
        linguisticAccuracy: translatedData.quality?.linguisticAccuracy || 85,
        culturalAdaptation: translatedData.quality?.culturalAdaptation || 80,
      },
    };

    // Preserve important existing fields during translation
    if (article.featuredImage) {
      updateData.featuredImage = article.featuredImage;
    }

    // Preserve other metadata fields that shouldn't be lost during translation
    if (article.categories && article.categories.length > 0) {
      updateData.categories = article.categories;
    }

    if (article.placement) {
      updateData.placement = article.placement;
    }

    if (article.pinned !== undefined) {
      updateData.pinned = article.pinned;
    }

    if (article.trending !== undefined) {
      updateData.trending = article.trending;
    }

    if (article.relatedCompanies && article.relatedCompanies.length > 0) {
      updateData.relatedCompanies = article.relatedCompanies;
    }

    // Update workflow stage to "translated" if it's currently "candidate-article"
    if (article.workflowStage === 'candidate-article') {
      updateData.workflowStage = 'translated';
    }

    // Update the article
    const updatedArticle = await payload.update({
      collection: 'articles',
      id: articleId,
      data: updateData,
    });

    console.log(
      `✅ German ${isReTranslation ? 're-' : ''}translation completed for article ${articleId}`
    );

    if (isReTranslation) {
      console.log('🔄 Re-translation summary:');
      console.log('  New German title:', cleanGermanTitle);
      console.log('  Content length:', germanContent.length, 'characters');
      console.log(
        '  Processing time:',
        translationResult.metrics.processingTime,
        'ms'
      );
    }

    // ✅ SPRINT 3: Standardized API Response Format
    return NextResponse.json({
      success: true,
      message: `Article ${isReTranslation ? 're-' : ''}translated successfully`,
      data: {
        // German tab updates (matches form field structure)
        germanTab: {
          germanTitle: cleanGermanTitle,
          germanSummary: germanSummary,
          germanContent: germanContentLexical,
          germanKeyInsights: germanKeyInsights,
          germanKeywords: germanKeywords,
        },
        // Main field updates
        workflowStage:
          article.workflowStage === 'candidate-article'
            ? 'translated'
            : article.workflowStage,
        hasGermanTranslation: true,
      },
      metrics: {
        processingTime: translationResult.metrics.processingTime,
        linguisticAccuracy: translatedData.quality?.linguisticAccuracy || 85,
        culturalAdaptation: translatedData.quality?.culturalAdaptation || 80,
      },
    });
  } catch (error: any) {
    console.error('❌ Error translating article:', error);

    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Unknown error occurred during translation',
      },
      { status: 500 }
    );
  }
}
